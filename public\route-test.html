<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2563eb;
        }
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
    </style>
</head>
<body>
    <h1>Route Test Page</h1>

    <div class="card">
        <h2>Test Routes</h2>
        <p>Click the buttons below to test different routes in the application.</p>

        <div>
            <button onclick="window.location.href = '/invitation-confirmation'">Go to /invitation-confirmation</button>
            <button onclick="window.location.href = '/test-confirmation'">Go to /test-confirmation</button>
            <button onclick="window.location.href = '/direct-confirmation'">Go to /direct-confirmation</button>
            <button onclick="window.location.href = '/simple-confirmation'">Go to /simple-confirmation</button>
            <button onclick="window.location.href = '/simple-confirmation?token=test-token'">Go to /simple-confirmation with token</button>
            <button onclick="window.location.href = '/dashboard'">Go to /dashboard</button>
            <button onclick="window.location.href = '/login'">Go to /login</button>
            <button onclick="window.location.href = '/register'">Go to /register</button>
            <button onclick="window.location.href = '/invitation/accept'">Go to /invitation/accept</button>
        </div>
    </div>

    <div class="card">
        <h2>Other Test Pages</h2>
        <p>Additional test pages to help with debugging.</p>

        <div>
            <button onclick="window.location.href = '/invitation-test.html'">Go to Invitation Test Page</button>
            <button onclick="window.location.href = '/get-tokens.html'">Go to Get Tokens Page</button>
        </div>
    </div>
</body>
</html>

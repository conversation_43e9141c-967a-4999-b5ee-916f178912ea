/**
 * GetStream Channel API Route
 *
 * This API route handles operations on a specific GetStream channel.
 * It's designed to work as a serverless function on Vercel.
 */

import { StreamChat } from 'stream-chat';

// Load environment variables
// For Vercel deployment, use GETSTREAM_API_KEY and GETSTREAM_API_SECRET
// For local development, use VITE_GETSTREAM_API_KEY and VITE_GETSTREAM_API_SECRET
const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY;
const apiSecret = process.env.GETSTREAM_API_SECRET || process.env.VITE_GETSTREAM_API_SECRET;

// Create a server-side client for GetStream
let serverClient;

// Initialize the server client if API key and secret are available
if (apiKey && apiSecret) {
  serverClient = StreamChat.getInstance(apiKey, apiSecret);
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Get the channel ID from the URL
  const { channelId } = req.query;

  if (!channelId) {
    return res.status(400).json({ error: 'Channel ID is required' });
  }

  // Check if API key and secret are available
  if (!apiKey || !apiSecret || !serverClient) {
    console.error('Error: GetStream API key or secret is missing in environment variables.');
    return res.status(500).json({ error: 'Server configuration error' });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      // Get channel details
      try {
        console.log('Getting channel:', channelId);
        
        // Try to get the channel
        const channel = serverClient.channel('messaging', channelId);
        const state = await channel.query();
        
        console.log('Channel found:', channelId);
        
        return res.status(200).json({
          channelId,
          channel: channel.id,
          members: state.members,
          status: 'found'
        });
      } catch (error) {
        console.error('Error getting channel:', error);
        return res.status(404).json({ error: 'Channel not found' });
      }
      
    case 'DELETE':
      // Delete the channel
      try {
        console.log('Deleting channel:', channelId);
        
        // Get the channel
        const channel = serverClient.channel('messaging', channelId);
        
        // Delete the channel
        await channel.delete();
        
        console.log('Channel deleted successfully:', channelId);
        
        return res.status(200).json({ 
          success: true,
          message: `Channel ${channelId} deleted successfully`
        });
      } catch (error) {
        console.error('Error deleting channel:', error);
        return res.status(500).json({ error: 'Failed to delete channel' });
      }
      
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

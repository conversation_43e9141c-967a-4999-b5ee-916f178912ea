/**
 * GetStream Debug API Route
 *
 * This endpoint helps diagnose GetStream configuration issues
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,POST');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Check environment variables
    const apiKey = process.env.GETSTREAM_API_KEY || process.env.VITE_GETSTREAM_API_KEY;
    const apiSecret = process.env.GETSTREAM_API_SECRET || process.env.VITE_GETSTREAM_API_SECRET;
    const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

    const diagnostics = {
      timestamp: new Date().toISOString(),
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        hasGetStreamApiKey: !!apiKey,
        hasGetStreamApiSecret: !!apiSecret,
        hasSupabaseUrl: !!supabaseUrl,
        hasSupabaseServiceKey: !!supabaseServiceKey,
        getStreamApiKeyLength: apiKey ? apiKey.length : 0,
        getStreamApiSecretLength: apiSecret ? apiSecret.length : 0
      },
      request: {
        method: req.method,
        headers: req.headers,
        body: req.body
      }
    };

    // Try to initialize GetStream client
    try {
      const { StreamChat } = await import('stream-chat');
      if (apiKey && apiSecret) {
        const serverClient = StreamChat.getInstance(apiKey, apiSecret);
        diagnostics.getstream = {
          clientInitialized: true,
          clientType: typeof serverClient
        };
      } else {
        diagnostics.getstream = {
          clientInitialized: false,
          error: 'Missing API key or secret'
        };
      }
    } catch (error) {
      diagnostics.getstream = {
        clientInitialized: false,
        error: error.message
      };
    }

    // Try to initialize Supabase client
    try {
      const { createClient } = await import('@supabase/supabase-js');
      if (supabaseUrl && supabaseServiceKey) {
        const supabase = createClient(supabaseUrl, supabaseServiceKey);
        diagnostics.supabase = {
          clientInitialized: true,
          clientType: typeof supabase
        };
      } else {
        diagnostics.supabase = {
          clientInitialized: false,
          error: 'Missing Supabase URL or service key'
        };
      }
    } catch (error) {
      diagnostics.supabase = {
        clientInitialized: false,
        error: error.message
      };
    }

    res.status(200).json(diagnostics);

  } catch (error) {
    res.status(500).json({
      error: 'Debug endpoint failed',
      message: error.message,
      stack: error.stack
    });
  }
}

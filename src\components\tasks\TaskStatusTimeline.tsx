import { CheckCircle, Clock, DollarSign, Hourglass, Loader2, MessageSquare, PlayCircle, ThumbsUp, User } from "lucide-react";
import { cn } from "@/lib/utils";

// Define the possible task statuses in order
const EXTERNAL_TASK_STATUSES = [
  'open',           // Task is created and open
  'interest',       // Suppliers have expressed interest
  'questions',      // Discussion phase between admin and suppliers
  'offer',          // Suppliers have submitted offers
  'assigned',       // Task has been assigned to someone
  'in_progress',    // Work has started on the task
  'completed',      // Work is completed, awaiting confirmation
  'closed',         // Task is closed by admin
  'pending_payment' // Payment is required to complete the task
];

// Define simplified workflow for internal tasks
const INTERNAL_TASK_STATUSES = [
  'assigned',       // Task has been assigned to internal staff
  'in_progress',    // Work has started on the task
  'completed',      // Work is completed, awaiting confirmation
  'closed'          // Task is closed by admin
];

// Define status labels for display
const STATUS_LABELS: Record<string, string> = {
  'open': 'Posted',
  'interest': 'Interest',
  'questions': 'Discussion',
  'offer': 'Offers',
  'assigned': 'Assigned',
  'in_progress': 'In Progress',
  'completed': 'Completed',
  'closed': 'Closed',
  'pending_payment': 'Payment Due'
};

// Define icons for each status
const STATUS_ICONS: Record<string, React.ReactNode> = {
  'open': <Clock size={16} />,
  'interest': <MessageSquare size={16} />,
  'questions': <MessageSquare size={16} />,
  'offer': <DollarSign size={16} />,
  'assigned': <User size={16} />,
  'in_progress': <PlayCircle size={16} />,
  'completed': <CheckCircle size={16} />,
  'pending_payment': <DollarSign size={16} />,
  'closed': <ThumbsUp size={16} />
};

interface TaskStatusTimelineProps {
  status: 'open' | 'interest' | 'questions' | 'offer' | 'assigned' | 'in_progress' | 'pending_payment' | 'completed' | 'closed';
  offersCount?: number;
  isCompact?: boolean; // For compact display in cards
  className?: string;
  visibility?: 'admin' | 'internal' | 'public'; // Task visibility to determine workflow
}

const TaskStatusTimeline = ({
  status,
  offersCount = 0,
  isCompact = false,
  className,
  visibility = 'public' // Default to public for backward compatibility
}: TaskStatusTimelineProps) => {
  // Determine which status array to use based on task visibility
  const isInternalTask = visibility === 'internal';
  const taskStatuses = isInternalTask ? INTERNAL_TASK_STATUSES : EXTERNAL_TASK_STATUSES;

  // Log which workflow we're using
  console.log(`Using ${isInternalTask ? 'internal' : 'external'} task workflow for task with visibility: ${visibility}`);
  console.log(`TaskStatusTimeline: Received status "${status}", available statuses:`, taskStatuses);

  // Ensure we have a valid status, default to first status in the appropriate workflow
  const defaultStatus = isInternalTask ? 'assigned' : 'open';
  const validStatus = taskStatuses.includes(status) ? status : defaultStatus;

  if (validStatus !== status) {
    console.warn(`TaskStatusTimeline: Status "${status}" not found in ${isInternalTask ? 'internal' : 'external'} workflow, defaulting to "${validStatus}"`);
  }

  // Find the current status index
  const currentStatusIndex = taskStatuses.indexOf(validStatus);

  // Determine if we should show offers count (only relevant for external tasks)
  const showOffers = !isInternalTask && (validStatus === 'open' || validStatus === 'offer') && offersCount > 0;

  return (
    <div className={cn("w-full", className)}>
      {isCompact ? (
        // Compact timeline for cards
        <div className="flex items-center justify-between w-full">
          {taskStatuses.map((stepStatus, index) => {
            // Determine if this step is active, completed, or upcoming
            const isActive = stepStatus === validStatus;
            const isCompleted = index < currentStatusIndex;
            const isUpcoming = index > currentStatusIndex;

            return (
              <div
                key={stepStatus}
                className={cn(
                  "flex flex-col items-center",
                  isActive ? "text-blue-600" :
                  isCompleted ? "text-green-600" :
                  "text-gray-300"
                )}
              >
                {/* Status icon */}
                <div className={cn(
                  "rounded-full p-1",
                  isActive ? "bg-blue-100" :
                  isCompleted ? "bg-green-100" :
                  "bg-gray-100"
                )}>
                  {STATUS_ICONS[stepStatus]}
                </div>

                {/* Only show the active status label in compact mode */}
                {isActive && (
                  <span className="text-xs mt-1 font-medium">
                    {STATUS_LABELS[stepStatus]}
                    {showOffers && (stepStatus === 'open' || stepStatus === 'offer') && ` (${offersCount})`}
                  </span>
                )}
              </div>
            );
          })}
        </div>
      ) : (
        // Full timeline for detailed views
        <div className="flex flex-col w-full">
          <div className="flex items-center justify-between w-full relative">
            {/* Connecting line behind the icons */}
            <div className="absolute top-5 left-0 right-0 h-0.5 bg-gray-200 -z-10"></div>

            {taskStatuses.map((stepStatus, index) => {
              // Determine if this step is active, completed, or upcoming
              const isActive = stepStatus === validStatus;
              const isCompleted = index < currentStatusIndex;
              const isUpcoming = index > currentStatusIndex;

              return (
                <div
                  key={stepStatus}
                  className={cn(
                    "flex flex-col items-center",
                    isActive ? "text-blue-600" :
                    isCompleted ? "text-green-600" :
                    "text-gray-300"
                  )}
                >
                  {/* Status icon with larger size and better styling */}
                  <div className={cn(
                    "rounded-full p-2 border-2 shadow-sm",
                    isActive ? "bg-blue-100 border-blue-300" :
                    isCompleted ? "bg-green-100 border-green-300" :
                    "bg-gray-100 border-gray-200"
                  )}>
                    {STATUS_ICONS[stepStatus]}
                  </div>

                  {/* Status label with better spacing */}
                  <span className={cn(
                    "text-xs mt-2",
                    isActive ? "font-bold" :
                    isCompleted ? "font-medium" :
                    "font-normal"
                  )}>
                    {STATUS_LABELS[stepStatus]}
                    {showOffers && (stepStatus === 'open' || stepStatus === 'offer') && ` (${offersCount})`}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Progress bar with better styling */}
          <div className="w-full h-2 bg-gray-200 mt-6 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 rounded-full transition-all duration-300"
              style={{
                width: `${Math.max(
                  (currentStatusIndex / (taskStatuses.length - 1)) * 100,
                  5
                )}%`
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskStatusTimeline;

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  ArrowLeft,
  MessageSquare,
  Calendar,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  FileText,
  WifiOff,
  RefreshCw,
  Image,
  PoundSterling,
  Building,
  ChevronLeft,
  ChevronRight,
  X
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { isOnline, isPWA, getCachedTasks, registerConnectivityListeners } from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import PWASimplifiedTimeline from './PWASimplifiedTimeline';
import PWAAdminReviewStatus from './PWAAdminReviewStatus';
import { format } from 'date-fns';

const PWASimplifiedTaskView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [task, setTask] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        fetchTask(); // Refresh data when coming back online
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Function to fetch task data
  const fetchTask = async () => {
    if (!id || !user) return;

    try {
      setLoading(true);
      setError(null);

      // If offline and PWA, use cached data
      if (!isOnline() && isPWA()) {
        console.log('[PWASimplifiedTaskView] Using cached data in offline mode');

        // Get cached tasks
        const cachedTasks = getCachedTasks();
        const cachedTask = cachedTasks.find(t => t.id === id);

        if (cachedTask) {
          setTask(cachedTask);
        } else {
          setError('Task not found in offline cache');
        }
      } else {
        // Fetch task from API - without trying to join profiles directly
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*, getstream_channel_id, chat_migrated_to_stream')
          .eq('id', id)
          .single();

        if (taskError) {
          console.error('[PWASimplifiedTaskView] Error fetching task:', taskError);
          throw taskError;
        }

        if (!taskData) {
          setError('Task not found');
          return;
        }

        console.log('[PWASimplifiedTaskView] Task data retrieved:', taskData);

        // Now fetch the creator's profile separately
        let creatorProfile = null;
        if (taskData.user_id) {
          const { data: creatorData, error: creatorError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.user_id)
            .maybeSingle();

          if (creatorError) {
            console.error('[PWASimplifiedTaskView] Error fetching creator profile:', creatorError);
          } else if (creatorData) {
            creatorProfile = creatorData;
          }
        }

        // Fetch the assignee's profile separately
        let assigneeProfile = null;
        if (taskData.assigned_to) {
          const { data: assigneeData, error: assigneeError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', taskData.assigned_to)
            .maybeSingle();

          if (assigneeError) {
            console.error('[PWASimplifiedTaskView] Error fetching assignee profile:', assigneeError);
          } else if (assigneeData) {
            assigneeProfile = assigneeData;
          }
        }

        // Combine the data
        const data = {
          ...taskData,
          created_by_profile: creatorProfile,
          assigned_to_profile: assigneeProfile
        };

        console.log('[PWASimplifiedTaskView] Combined task data:', data);
        setTask(data);
      }
    } catch (error: any) {
      console.error('[PWASimplifiedTaskView] Error fetching task:', error);
      setError(error.message || 'Failed to load task');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchTask();
  }, [id]);

  // Handle back button
  const handleBack = () => {
    navigate(-1);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'dd MMM yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'interest':
      case 'questions':
        return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'offer':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'assigned':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'completed':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'closed':
        return 'bg-teal-100 text-teal-800 border-teal-200';
      case 'pending_payment':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status) {
      case 'pending_payment':
        return 'Payment Required';
      case 'in_progress':
        return 'In Progress';
      case 'interest':
        return 'Interest Expressed';
      case 'questions':
        return 'Discussion Phase';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle chat button
  const handleChat = () => {
    try {
      if (!task) return;

      console.log('[PWASimplifiedTaskView] Handling chat button click for task:', task.id);

      // For PWA, we'll use the GetStream channel ID if available
      // If not, we'll use the task ID as the channel ID (format: task-{taskId})
      const channelId = task.getstream_channel_id || `task-${task.id}`;

      if (channelId) {
        console.log('[PWASimplifiedTaskView] Navigating to chat with channelId:', channelId);

        // Make sure we're using the correct channel ID format
        navigate(`/messages/${channelId}?task=${task.id}`);

        // If the task doesn't have a GetStream channel ID yet, update it
        if (!task.getstream_channel_id || !task.chat_migrated_to_stream) {
          console.log('[PWASimplifiedTaskView] Updating task with GetStream channel ID');

          // Update the task in the background
          supabase
            .from('tasks')
            .update({
              chat_migrated_to_stream: true,
              getstream_channel_id: channelId,
              updated_at: new Date().toISOString(),
            })
            .eq('id', task.id)
            .then(({ error }) => {
              if (error) {
                console.error('[PWASimplifiedTaskView] Error updating task:', error);
              } else {
                console.log('[PWASimplifiedTaskView] Task updated with GetStream channel ID');
              }
            });
        }
      } else {
        console.error('[PWASimplifiedTaskView] No channel ID available for chat');
        setError('Cannot open chat - no channel ID available');
      }
    } catch (error) {
      console.error('[PWASimplifiedTaskView] Error in handleChat:', error);
      setError('Failed to open chat');
    }
  };

  // Handle image gallery
  const openImageGallery = (index: number) => {
    setSelectedImageIndex(index);
  };

  const closeImageGallery = () => {
    setSelectedImageIndex(null);
  };

  const goToPreviousImage = () => {
    if (selectedImageIndex === null || !task?.images) return;
    setSelectedImageIndex((selectedImageIndex - 1 + task.images.length) % task.images.length);
  };

  const goToNextImage = () => {
    if (selectedImageIndex === null || !task?.images) return;
    setSelectedImageIndex((selectedImageIndex + 1) % task.images.length);
  };

  return (
    <PWAMobileLayout>
      <div className="container max-w-md mx-auto px-4 py-4">
        {/* Header with back button */}
        <div className="flex items-center mb-4">
          <Button variant="ghost" size="icon" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold">Task Details</h1>
        </div>

        {/* Offline indicator */}
        {offlineMode && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4 flex items-center">
            <WifiOff className="h-5 w-5 text-yellow-500 mr-2" />
            <div className="text-sm text-yellow-700">
              You're offline. Some data may not be up to date.
            </div>
          </div>
        )}

        {/* Error state */}
        {error && (
          <Card className="mb-4">
            <CardContent className="p-4 text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-red-500">{error}</p>
              <Button variant="outline" className="mt-3" onClick={fetchTask}>
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        )}

        {/* Task content */}
        {!loading && task && (
          <>
            {/* Task header */}
            <div className="mb-4">
              <h2 className="text-xl font-bold mb-2">{task.title}</h2>
              <div className="flex items-center justify-between mb-2">
                <Badge variant={getStatusVariant(task.status)} className="text-sm">
                  {formatStatus(task.status)}
                </Badge>
                <div className="text-sm text-gray-500">
                  {formatDate(task.created_at)}
                </div>
              </div>
            </div>

            {/* Admin Review Status (if applicable) */}
            {task.visibility === 'admin' && task.status === 'open' && (
              <PWAAdminReviewStatus className="mb-4" />
            )}

            {/* Simplified timeline */}
            <Card className="mb-4">
              <CardContent className="p-3">
                <PWASimplifiedTimeline
                  status={task.status}
                  visibility={task.visibility}
                />
              </CardContent>
            </Card>

            {/* Task details */}
            <Card className="mb-4">
              <CardContent className="p-4 space-y-3">
                <div className="space-y-1">
                  <h3 className="text-sm font-medium text-gray-500">Description</h3>
                  <p className="text-sm">{task.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Location</h3>
                    <div className="flex items-center text-sm">
                      <MapPin size={14} className="mr-1 text-gray-400" />
                      <span>{task.location}</span>
                    </div>
                    {task.building && (
                      <div className="flex items-center text-sm">
                        <Building size={14} className="mr-1 text-gray-400" />
                        <span>{task.building}</span>
                        {task.room && <span> - Room {task.room}</span>}
                      </div>
                    )}
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-sm font-medium text-gray-500">Due Date</h3>
                    <div className="flex items-center text-sm">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      <span>{formatDate(task.due_date) || 'Not specified'}</span>
                    </div>
                    {task.budget > 0 && (
                      <div className="flex items-center text-sm">
                        <PoundSterling size={14} className="mr-1 text-gray-400" />
                        <span>£{task.budget.toFixed(2)}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* People involved */}
                <div className="pt-2 border-t">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">People</h3>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8 mr-2">
                        <AvatarFallback>
                          {getInitials(task.created_by_profile?.first_name + ' ' + task.created_by_profile?.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">
                          {task.created_by_profile?.first_name} {task.created_by_profile?.last_name}
                        </p>
                        <p className="text-xs text-gray-500">Created by</p>
                      </div>
                    </div>

                    {task.assigned_to_profile && (
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8 mr-2">
                          <AvatarFallback>
                            {getInitials(task.assigned_to_profile?.first_name + ' ' + task.assigned_to_profile?.last_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">
                            {task.assigned_to_profile?.first_name} {task.assigned_to_profile?.last_name}
                          </p>
                          <p className="text-xs text-gray-500">Assigned to</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Task images */}
            {task.images && task.images.length > 0 && (
              <Card className="mb-4">
                <CardContent className="p-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Images</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {task.images.map((image: string, index: number) => (
                      <div
                        key={index}
                        className="aspect-square rounded-md overflow-hidden border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                        onClick={() => openImageGallery(index)}
                      >
                        <img
                          src={image}
                          alt={`Task image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action buttons */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handleChat}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Chat
              </Button>

              <Button
                variant="default"
                className="flex-1"
                onClick={() => {
                  console.log(`[PWASimplifiedTaskView] Navigating to task actions: /tasks/${id}/actions`);
                  navigate(`/tasks/${id}/actions`);
                }}
              >
                Actions
              </Button>
            </div>

            {/* Image lightbox */}
            <Dialog open={selectedImageIndex !== null} onOpenChange={closeImageGallery}>
              <DialogContent className="max-w-full p-0 h-[90vh] bg-black/90 border-none">
                <div className="relative flex items-center justify-center h-full">
                  {selectedImageIndex !== null && task.images && (
                    <img
                      src={task.images[selectedImageIndex]}
                      alt={`Task image ${selectedImageIndex + 1}`}
                      className="max-h-full max-w-full object-contain"
                    />
                  )}

                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute left-2 text-white hover:bg-black/50"
                    onClick={goToPreviousImage}
                  >
                    <ChevronLeft className="h-8 w-8" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 text-white hover:bg-black/50"
                    onClick={goToNextImage}
                  >
                    <ChevronRight className="h-8 w-8" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2 text-white hover:bg-black/50"
                    onClick={closeImageGallery}
                  >
                    <X className="h-6 w-6" />
                  </Button>

                  {selectedImageIndex !== null && task.images && (
                    <div className="absolute bottom-4 left-0 right-0 text-center text-white text-sm">
                      {selectedImageIndex + 1} / {task.images.length}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </>
        )}
      </div>
    </PWAMobileLayout>
  );
};

export default PWASimplifiedTaskView;
